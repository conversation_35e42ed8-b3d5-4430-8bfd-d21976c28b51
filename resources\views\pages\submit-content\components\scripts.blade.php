<script>
  var dropzoneUpload = new Dropzone('.dropzone', {
    uploadMultiple: true,
    acceptedFiles: ".jpg,.jpeg,.png,.tiff,.tif,.mp4,.mov,.mp3,.wav,.avi,.m4a,.docx,.doc,.pdf,.xls,.xlsx,.xlsm",
    maxFileSize: 50,
    addRemoveLinks: false,
    init: function () {
      this.on("success", function (file, response) {
        hasUploadError = false;
        console.log("Upload berhasil", response);
      });

      this.on("queuecomplete", function () {
        console.log("Semua file selesai diupload!");
      });

      this.on("error", function (file, errorMessage, xhr) {
        let message = '';
        hasUploadError = true;

        if (xhr && xhr.response) {
            try {
                let response = JSON.parse(xhr.response);
                message = response.message || "<PERSON>r<PERSON><PERSON> kesalahan saat mengunggah file.";
            } catch (e) {
                message = "<PERSON><PERSON><PERSON><PERSON> kesalahan pada server.";
            }
        } else {
            message = typeof errorMessage === "string" ? errorMessage : "File tidak bisa diunggah.";
        }

        file.previewElement.classList.add("dz-error");
        const errorDisplay = file.previewElement.querySelector("[data-dz-errormessage]");
        if (errorDisplay) {
            errorDisplay.textContent = message;
        }
      });

      this.on("removedfile", function(file) {
        console.log("File removed:", file.name);
      });

      this.on("addedfile", function(file) {
        // // Check file type and show error if not supported
        // const acceptedTypes = [
        //   // Images
        //   'image/jpeg', 'image/png', 'image/tiff', 
        //   // Videos
        //   'video/mp4', 'video/quicktime',
        //   // Audio
        //   'audio/mpeg', 'audio/wav', 'audio/x-wav', 
        //   // Documents
        //   'application/pdf', 
        //   'application/msword', 
        //   'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        //   // Excel
        //   'application/vnd.ms-excel',
        //   'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        //   'application/vnd.ms-excel.sheet.macroEnabled.12',
        //   'application/octet-stream' // For some Excel files
        // ];
        
        // // Special handling for Excel files with .xls extension which might have different MIME types
        // const fileName = file.name.toLowerCase();
        // const isExcelFile = fileName.endsWith('.xls') || 
        //                   fileName.endsWith('.xlsx') || 
        //                   fileName.endsWith('.xlsm');
        
        // if (!acceptedTypes.includes(file.type) && !isExcelFile) {
        //   this.removeFile(file);
        //   alert("Jenis file tidak didukung. Harap unggah file dengan format yang sesuai.");
        //   return false;
        // }
        
        setTimeout(function() {
          let errorMark = file.previewElement.querySelector(".dz-error-mark");

          if (!errorMark) {
            errorMark = document.createElement('div');
            errorMark.className = 'dz-error-mark';
            file.previewElement.appendChild(errorMark);
          }

          errorMark.style.pointerEvents = "auto";
          errorMark.style.cursor = "pointer";
          errorMark.style.zIndex = "1000";
          errorMark.style.opacity = "1";
          errorMark.style.visibility = "visible";
          errorMark.style.backgroundColor = "rgba(0, 0, 0, 0.7)";
          errorMark.style.borderRadius = "50%";
          errorMark.style.padding = "8px";
          errorMark.style.boxShadow = "0 2px 5px rgba(0, 0, 0, 0.3)";
          errorMark.style.transition = "all 0.2s ease";
          errorMark.style.position = "absolute";
          errorMark.style.top = "50%";
          errorMark.style.left = "50%";
          errorMark.style.transform = "translate(-50%, -50%)";

          errorMark.innerHTML = `
            <svg width="30" height="30" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M18 6L6 18" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M6 6L18 18" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          `;

          errorMark.addEventListener("click", function(e) {
            e.preventDefault();
            e.stopPropagation();
            dropzoneUpload.removeFile(file);
          });

          errorMark.addEventListener("mouseenter", function() {
            errorMark.style.backgroundColor = "rgba(0, 0, 0, 0.9)";
            errorMark.style.boxShadow = "0 4px 8px rgba(0, 0, 0, 0.5)";
            errorMark.style.transform = "translate(-50%, -50%) scale(1.1)";
          });

          errorMark.addEventListener("mouseleave", function() {
            errorMark.style.backgroundColor = "rgba(0, 0, 0, 0.7)";
            errorMark.style.boxShadow = "0 2px 5px rgba(0, 0, 0, 0.3)";
            errorMark.style.transform = "translate(-50%, -50%)";
          });
        }, 100);
      });
    }
  });

  document.head.insertAdjacentHTML('beforeend', `
    <style>
      /* We're not using the remove link anymore */
      .dz-error-mark {
        pointer-events: auto !important;
        cursor: pointer !important; /* This makes the cursor change to a hand/pointer when hovering */
        z-index: 1000 !important;
        opacity: 1 !important;
        transition: transform 0.2s ease, background-color 0.2s ease !important;
      }

      /* Add hover effect to make it clear it's clickable */
      .dz-error-mark:hover {
        transform: scale(1.1) !important;
        background-color: rgba(0, 0, 0, 0.9) !important;
      }
      .dz-error-message {
        pointer-events: none !important;
      }
      /* Ensure the preview container doesn't block the remove button */
      .dz-preview {
        position: relative !important;
      }
      /* Make sure the error message doesn't overlap the remove button */
      .dz-error-message {
        margin-bottom: 30px !important;
      }

      /* Ensure the error mark (X in circle) is properly positioned and visible */
      .dz-error-mark svg {
        display: block !important;
      }

      /* Make the error mark more visible and interactive */
      .dz-error .dz-error-mark {
        display: block !important;
        opacity: 1 !important;
        visibility: visible !important;
        background-color: rgba(0, 0, 0, 0.7) !important;
        border-radius: 50% !important;
        padding: 8px !important;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3) !important;
        transition: all 0.2s ease !important;
        position: absolute !important;
        top: 50% !important;
        left: 50% !important;
        transform: translate(-50%, -50%) !important;
      }

      /* Hover effect for the error mark */
      .dz-error .dz-error-mark:hover {
        background-color: rgba(0, 0, 0, 0.9) !important;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.5) !important;
        transform: translate(-50%, -50%) scale(1.1) !important;
      }

      /* Make the SVG white and more visible */
      .dz-error .dz-error-mark svg {
        width: 30px !important;
        height: 30px !important;
      }

      .dz-error .dz-error-mark svg path {
        fill: white !important;
      }
    </style>
  `);

  let hasUploadError = false;
  dropzoneUpload.on('queuecomplete', function(file, res){
    if(hasUploadError) return;
    setTimeout(() => {
      location.reload()
    }, 2500);
  })
</script>
