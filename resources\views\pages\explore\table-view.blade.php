{{-- Table View for Explore Page --}}
<div class="table-view-container">
  <table class="table-view">
    <thead>
      <tr>
        <th class="name-column">Name</th>
        <th class="date-column">Date Uploaded</th>
        <th class="type-column">Type</th>
        <th class="creator-column">Creator</th>
      </tr>
    </thead>
    <tbody>
      @foreach ($data as $item)
      <tr class="table-row cursor-pointer" data-id="{{ $item->id }}" data-modal-toggle="#detail-modal-{{ $item->id }}">
        <td class="name-column">
          <div class="file-info">
            <div class="file-icon">
              @php
                $iconClass = '';
                $iconColor = '';

                if($item->media_type == "Document") {
                  if(str_ends_with(strtolower($item->filename), '.pdf')) {
                    $iconClass = 'fa-solid fa-file-pdf';
                    $iconColor = '#e53e3e'; // Merah untuk PDF
                  } elseif(str_ends_with(strtolower($item->filename), '.doc') || str_ends_with(strtolower($item->filename), '.docx')) {
                    $iconClass = 'fa-solid fa-file-word';
                    $iconColor = '#3182ce'; // Biru untuk Word
                  } elseif(str_ends_with(strtolower($item->filename), '.xls') || str_ends_with(strtolower($item->filename), '.xlsx')) {
                    $iconClass = 'fa-solid fa-file-excel';
                    $iconColor = '#38a169'; // Hijau untuk Excel
                  } elseif(str_ends_with(strtolower($item->filename), '.ppt') || str_ends_with(strtolower($item->filename), '.pptx')) {
                    $iconClass = 'fa-solid fa-file-powerpoint';
                    $iconColor = '#dd6b20'; // Oranye untuk PowerPoint
                  } elseif(str_ends_with(strtolower($item->filename), '.txt')) {
                    $iconClass = 'fa-solid fa-file-lines';
                    $iconColor = '#718096'; // Abu-abu untuk TXT
                  } else {
                    $iconClass = 'fa-solid fa-file';
                    $iconColor = '#718096'; // Abu-abu untuk file lainnya
                  }
                } elseif($item->media_type == "Audio") {
                  $iconClass = 'fa-solid fa-file-audio';
                  $iconColor = '#805ad5'; // Ungu untuk Audio
                } elseif($item->media_type == "Video") {
                  $iconClass = 'fa-solid fa-file-video';
                  $iconColor = '#dd6b20'; // Oranye untuk Video
                } else {
                  $iconClass = 'fa-solid fa-file-image';
                  $iconColor = '#38a169'; // Hijau untuk Image
                }
              @endphp
              <i class="{{ $iconClass }}" style="color: {{ $iconColor }} !important;"></i>
            </div>
            <span class="file-name">{{ $item->filename }}</span>
          </div>
        </td>
        <td class="date-column">{{ \Carbon\Carbon::parse($item->upload_date)->setTimezone('Asia/Jakarta')->format('d/m/Y H:i') }}</td>
        <td class="type-column">{{ $item->media_type }} File</td>
        <td class="creator-column">{{ $item->creator_name ?? 'Unknown' }}</td>
      </tr>
      @endforeach
    </tbody>
  </table>
</div>
