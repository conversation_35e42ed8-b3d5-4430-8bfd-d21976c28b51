<?php

namespace App\Repositories;

use App\Models\Content;
use App\Repositories\Contracts\ContentRepositoryInterface;

class ContentRepository implements ContentRepositoryInterface
{
  public function findOne($id)
  {
    $user_id = auth()->user()->id;

    return Content::with([
      'category:id,name',
      'category_2:id,name',
      'author:id,name',
      'keywords:id,name',
      'favorite' => function ($q) use ($user_id) {
        $q->where('user_id', $user_id);
      }
    ])->findOrFail($id);
  }

  public function getContent(array $query)
  {
    $content = Content::with([
      'category:id,name',
      'category_2:id,name',
      'favorites',
      'collections',
      'review' => function ($q) {
          $q->latest()->limit(1)->with('reviewer:id,name');;
        }
      ])
      ->select('contents.*')
      ->when(isset($query['author']), function ($q) use ($query){
        return $q->where('author_id', $query['author']);
      })
      ->when(isset($query['status']), function($q) use ($query){
        if (is_array($query['status'])) {
          // If Rejected is one of the statuses, only show those rejected within the last 14 days
          if (in_array('Rejected', $query['status'])) {
            return $q->where(function($subQuery) use ($query) {
              // For Rejected status, add date filter (last 14 days)
              $subQuery->where(function($rejectedQuery) {
                $rejectedQuery->where('status', 'Rejected')
                  ->where('updated_at', '>=', now()->subDays(14));
              });

              // For other statuses in the array, no date filter
              $otherStatuses = array_diff($query['status'], ['Rejected']);
              if (!empty($otherStatuses)) {
                $subQuery->orWhereIn('status', $otherStatuses);
              }
            });
          } else {
            // If Rejected is not in the array, proceed normally
            return $q->whereIn('status', $query['status']);
          }
        } else {
          // If it's a single status
          if ($query['status'] === 'Rejected' || $query['status'] === 'Approved') {
            // For Rejected/Approved status, add date filter (last 14 days)
            return $q->where('status', $query['status'])
              ->where('updated_at', '>=', now()->subDays(14));
          } else {
            return $q->where('status', $query['status']);
          }
        }
      })
      ->when(isset($query['media_type']), function ($q) use ($query) {
          if (is_array($query['media_type'])) {
              return $q->whereIn('media_type', $query['media_type']);
          } else {
              return $q->where('media_type', $query['media_type']);
          }
      })
      ->paginate(12);

    return $content;
  }

  public function getPublishedContent(array $query)
  {
    $user_id = auth()->user()->id;

    $content = Content::with([
        'category:id,name',
        'category_2:id,name',
        'author:id,name',
        'keywords:id,name',
        'favorite' => function ($q) use ($user_id) {
          $q->where('user_id', $user_id);
        }
      ])
      ->when(isset($query['search']), function ($q) use ($query) {
        $q->where(function ($subQuery) use ($query) {
          $subQuery->orWhereHas('category', function($qry) use ($query) {
              $qry->where('name', 'like', '%'.$query['search'].'%');
          });
          $subQuery->orWhereHas('category_2', function($qry) use ($query) {
              $qry->where('name', 'like', '%'.$query['search'].'%');
          });
          $subQuery->orWhereHas('keywords', function($qry) use ($query) {
              $qry->where('name', 'like', '%'.$query['search'].'%');
          });
          $subQuery->orWhere('description', 'like', '%'.$query['search'].'%');
        });
      })
      ->when(isset($query['media_type']), function ($q) use ($query) {
          $q->where('media_type', $query['media_type']);
      })
      ->when(isset($query['allowed_media_types']), function ($q) use ($query) {
          $q->whereIn('media_type', $query['allowed_media_types']);
      })
      ->when(isset($query['category_id']), function($q) use ($query) {
        $q->whereHas('category', function($qry) use ($query) {
          $qry->where('id', $query['category_id']);
        });
      })
      ->when(isset($query['category_2_id']), function($q) use ($query) {
        $q->whereHas('category_2', function($qry) use ($query) {
          $qry->where('id', $query['category_2_id']);
        });
      })
      ->when(isset($query['keywords']), function($q) use ($query) {
        $q->whereHas('keywords', function($qry) use ($query) {
          $qry->whereIn('name', $query['keywords']);
        });
      })
      ->when(isset($query['orientation']), function($q) use ($query) {
        $q->where('orientation', $query['orientation']);
      })
      ->where('status', 'Published')
      ->latest()
      ->paginate(100)
      ->withQueryString();

    return $content;
  }

  public function countContent()
  {
    // Get the date 14 days ago for filtering rejected content
    $fourteenDaysAgo = now()->subDays(14);

    // Enable query logging
    \DB::enableQueryLog();

    // Base query
    $query = Content::query();

    // Get counts for all statuses except Rejected
    $nonRejectedCounts = (clone $query)->selectRaw("
      SUM(CASE WHEN status = 'Pending' THEN 1 ELSE 0 END) as pending,
      SUM(CASE WHEN status = 'Pending' AND media_type IN ('Photo', 'Illustration') THEN 1 ELSE 0 END) as pending_image,
      SUM(CASE WHEN status = 'Pending' AND media_type = 'Video' THEN 1 ELSE 0 END) as pending_video,
      SUM(CASE WHEN status = 'Pending' AND media_type = 'Audio' THEN 1 ELSE 0 END) as pending_audio,
      SUM(CASE WHEN status = 'Pending' AND media_type = 'Document' THEN 1 ELSE 0 END) as pending_document,
      SUM(CASE WHEN status = 'Published' THEN 1 ELSE 0 END) as published,
      SUM(CASE WHEN status = 'Published' AND media_type IN ('Photo', 'Illustration') THEN 1 ELSE 0 END) as published_image,
      SUM(CASE WHEN status = 'Published' AND media_type = 'Video' THEN 1 ELSE 0 END) as published_video,
      SUM(CASE WHEN status = 'Published' AND media_type = 'Audio' THEN 1 ELSE 0 END) as published_audio,
      SUM(CASE WHEN status = 'Published' AND media_type = 'Document' THEN 1 ELSE 0 END) as published_document,
      /* Count both Approved and Published for approved counts since all approved content is published */
      SUM(CASE WHEN status IN ('Approved', 'Published') THEN 1 ELSE 0 END) as approved,
      SUM(CASE WHEN status IN ('Approved', 'Published') AND media_type IN ('Photo', 'Illustration') THEN 1 ELSE 0 END) as approved_image,
      SUM(CASE WHEN status IN ('Approved', 'Published') AND media_type = 'Video' THEN 1 ELSE 0 END) as approved_video,
      SUM(CASE WHEN status IN ('Approved', 'Published') AND media_type = 'Audio' THEN 1 ELSE 0 END) as approved_audio,
      SUM(CASE WHEN status IN ('Approved', 'Published') AND media_type = 'Document' THEN 1 ELSE 0 END) as approved_document
    ")->first();

    // Get counts for Rejected status with date filter (last 14 days)
    $rejectedCounts = (clone $query)->where('status', 'Rejected')
      ->where('updated_at', '>=', $fourteenDaysAgo)
      ->selectRaw("
        COUNT(*) as rejected,
        SUM(CASE WHEN media_type IN ('Photo', 'Illustration') THEN 1 ELSE 0 END) as rejected_image,
        SUM(CASE WHEN media_type = 'Video' THEN 1 ELSE 0 END) as rejected_video,
        SUM(CASE WHEN media_type = 'Audio' THEN 1 ELSE 0 END) as rejected_audio,
        SUM(CASE WHEN media_type = 'Document' THEN 1 ELSE 0 END) as rejected_document
      ")->first();

    // Calculate reviewed counts (Approved + Rejected)
    $reviewed = ($nonRejectedCounts->approved ?? 0) + ($rejectedCounts->rejected ?? 0);
    $reviewed_image = ($nonRejectedCounts->approved_image ?? 0) + ($rejectedCounts->rejected_image ?? 0);
    $reviewed_video = ($nonRejectedCounts->approved_video ?? 0) + ($rejectedCounts->rejected_video ?? 0);
    $reviewed_audio = ($nonRejectedCounts->approved_audio ?? 0) + ($rejectedCounts->rejected_audio ?? 0);
    $reviewed_document = ($nonRejectedCounts->approved_document ?? 0) + ($rejectedCounts->rejected_document ?? 0);

    // Combine all counts
    $counts = array_merge(
      (array) $nonRejectedCounts,
      (array) $rejectedCounts,
      [
        'reviewed' => $reviewed,
        'reviewed_image' => $reviewed_image,
        'reviewed_video' => $reviewed_video,
        'reviewed_audio' => $reviewed_audio,
        'reviewed_document' => $reviewed_document
      ]
    );

    // Convert any null values to 0
    foreach ($counts as $key => $value) {
      $counts[$key] = $value ?? 0;
    }

    // Log the queries
    \Illuminate\Support\Facades\Log::info('SQL Queries:', \DB::getQueryLog());

    // Log the counts
    \Illuminate\Support\Facades\Log::info('Content counts:', (array) $counts);

    return (object) $counts;
  }

  public function countContentByAuthor(string $author)
  {
    // Get the date 14 days ago for filtering rejected content
    $fourteenDaysAgo = now()->subDays(14);

    // Base query for author
    $query = Content::where('author_id', $author);

    // Get counts for all statuses except Rejected
    $nonRejectedCounts = (clone $query)->selectRaw("
        SUM(CASE WHEN status = 'Draft' THEN 1 ELSE 0 END) as draft,
        SUM(CASE WHEN status = 'Draft' AND media_type IN ('Photo', 'Illustration') THEN 1 ELSE 0 END) as draft_image,
        SUM(CASE WHEN status = 'Draft' AND media_type = 'Video' THEN 1 ELSE 0 END) as draft_video,
        SUM(CASE WHEN status = 'Draft' AND media_type = 'Audio' THEN 1 ELSE 0 END) as draft_audio,
        SUM(CASE WHEN status = 'Draft' AND media_type = 'Document' THEN 1 ELSE 0 END) as draft_document,
        SUM(CASE WHEN status = 'Pending' THEN 1 ELSE 0 END) as pending,
        SUM(CASE WHEN status = 'Pending' AND media_type IN ('Photo', 'Illustration') THEN 1 ELSE 0 END) as pending_image,
        SUM(CASE WHEN status = 'Pending' AND media_type = 'Video' THEN 1 ELSE 0 END) as pending_video,
        SUM(CASE WHEN status = 'Pending' AND media_type = 'Audio' THEN 1 ELSE 0 END) as pending_audio,
        SUM(CASE WHEN status = 'Pending' AND media_type = 'Document' THEN 1 ELSE 0 END) as pending_document,
        SUM(CASE WHEN status = 'Published' THEN 1 ELSE 0 END) as published,
        SUM(CASE WHEN status = 'Published' AND media_type IN ('Photo', 'Illustration') THEN 1 ELSE 0 END) as published_image,
        SUM(CASE WHEN status = 'Published' AND media_type = 'Video' THEN 1 ELSE 0 END) as published_video,
        SUM(CASE WHEN status = 'Published' AND media_type = 'Audio' THEN 1 ELSE 0 END) as published_audio,
        SUM(CASE WHEN status = 'Published' AND media_type = 'Document' THEN 1 ELSE 0 END) as published_document,
        /* Count both Approved and Published for approved counts since all approved content is published */
        SUM(CASE WHEN status IN ('Approved', 'Published') THEN 1 ELSE 0 END) as approved,
        SUM(CASE WHEN status IN ('Approved', 'Published') AND media_type IN ('Photo', 'Illustration') THEN 1 ELSE 0 END) as approved_image,
        SUM(CASE WHEN status IN ('Approved', 'Published') AND media_type = 'Video' THEN 1 ELSE 0 END) as approved_video,
        SUM(CASE WHEN status IN ('Approved', 'Published') AND media_type = 'Audio' THEN 1 ELSE 0 END) as approved_audio,
        SUM(CASE WHEN status IN ('Approved', 'Published') AND media_type = 'Document' THEN 1 ELSE 0 END) as approved_document
    ")->first();

    // Get counts for Rejected status with date filter (last 14 days)
    $rejectedCounts = (clone $query)->where('status', 'Rejected')
      ->where('updated_at', '>=', $fourteenDaysAgo)
      ->selectRaw("
        COUNT(*) as rejected,
        SUM(CASE WHEN media_type IN ('Photo', 'Illustration') THEN 1 ELSE 0 END) as rejected_image,
        SUM(CASE WHEN media_type = 'Video' THEN 1 ELSE 0 END) as rejected_video,
        SUM(CASE WHEN media_type = 'Audio' THEN 1 ELSE 0 END) as rejected_audio,
        SUM(CASE WHEN media_type = 'Document' THEN 1 ELSE 0 END) as rejected_document
      ")->first();

    // Calculate reviewed counts (Approved + Rejected)
    $reviewed = ($nonRejectedCounts->approved ?? 0) + ($rejectedCounts->rejected ?? 0);
    $reviewed_image = ($nonRejectedCounts->approved_image ?? 0) + ($rejectedCounts->rejected_image ?? 0);
    $reviewed_video = ($nonRejectedCounts->approved_video ?? 0) + ($rejectedCounts->rejected_video ?? 0);
    $reviewed_audio = ($nonRejectedCounts->approved_audio ?? 0) + ($rejectedCounts->rejected_audio ?? 0);
    $reviewed_document = ($nonRejectedCounts->approved_document ?? 0) + ($rejectedCounts->rejected_document ?? 0);

    // Combine all counts
    $counts = array_merge(
      (array) $nonRejectedCounts,
      (array) $rejectedCounts,
      [
        'reviewed' => $reviewed,
        'reviewed_image' => $reviewed_image,
        'reviewed_video' => $reviewed_video,
        'reviewed_audio' => $reviewed_audio,
        'reviewed_document' => $reviewed_document
      ]
    );

    // Convert any null values to 0
    foreach ($counts as $key => $value) {
      $counts[$key] = $value ?? 0;
    }

    return (object) $counts;
  }

  public function create(array $data)
  {
    return Content::insert($data);
  }

  public function update(string $id, array $data)
  {
    $content = $this->findOne($id);

    if(!$content) return $content;

    return $content->update($data);
  }

  public function delete(string $id)
  {
    $content = $this->findOne($id);

    if(!$content) return $content;

    return $content->delete();
  }

  public function bulkDelete(array $id)
  {
    try {
      return Content::whereIn('id', $id)->delete();
    } catch (\Exception $e) {
      throw $e;
    }
  }

}