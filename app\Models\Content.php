<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Content extends Model
{
    use HasFactory, HasUuids;

    protected $fillable = [
        'author_id',
        'category_id',
        'category_2_id',
        'description',
        'filename',
        'file_path',
        'thumbnail_path',
        'media_type',
        'orientation',
        'creator_name',
        'date_taken',
        'release_document',
        'status',
        'upload_date',
        'downloads_count',
    ];

    public function author(): BelongsTo
    {
        return $this->belongsTo(User::class, 'author_id');
    }

    public function category(): BelongsTo
    {
        return $this->belongsTo(Category::class, 'category_id');
    }

    public function category_2(): BelongsTo
    {
        return $this->belongsTo(Category::class, 'category_2_id');
    }

    public function keywords()
    {
        return $this->belongsToMany(Keyword::class, 'content_keywords'); 
    }

    public function favorite()
    {
        return $this->hasOne(Favorite::class, 'content_id');
    }

    public function favorites()
    {
        return $this->hasMany(Favorite::class, 'content_id');
    }

    public function collections()
    {
        return $this->belongsToMany(Collection::class, 'collection_contents', 'content_id', 'collection_id');
    }

    public function review()
    {
        return $this->hasMany(ContentReview::class, 'content_id');
    }
}
