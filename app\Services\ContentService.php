<?php

namespace App\Services;

use App\Models\User;
use App\Helpers\StorageHelper;
use App\Repositories\Contracts\CategoryRepositoryInterface;
use App\Repositories\Contracts\ContentRepositoryInterface;
use App\Repositories\Contracts\KeywordRepositoryInterface;
use App\Repositories\Contracts\LogRepositoryInterface;
use App\Repositories\Contracts\NotificationRepositoryInterface;
use App\Repositories\Contracts\ReviewRepositoryInterface;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Validation\Rules\Exists;
use Intervention\Image\Laravel\Facades\Image;
use Intervention\Image\ImageManager;
use ProtoneMedia\LaravelFFMpeg\Support\FFMpeg;
use Ramsey\Uuid\Uuid;
use Imagick;

class ContentService
{
  protected $contentRepository, $categoryRepository, $keywordRepository, $reviewRepository, $logRepository, $notificationRepository;

  public function __construct(
    ContentRepositoryInterface $contentRepository,
    CategoryRepositoryInterface $categoryRepository,
    KeywordRepositoryInterface $keywordRepository,
    ReviewRepositoryInterface $reviewRepository,
    LogRepositoryInterface $logRepository,
    NotificationRepositoryInterface $notificationRepository,
    )
  {
    $this->contentRepository = $contentRepository;
    $this->categoryRepository = $categoryRepository;
    $this->keywordRepository = $keywordRepository;
    $this->reviewRepository = $reviewRepository;
    $this->logRepository = $logRepository;
    $this->notificationRepository = $notificationRepository;
  }

  public function findOne(string $id)
  {
    return $this->contentRepository->findOne($id);
  }

  public function getContentByAuthor($request)
  {
    try {
      // if($request['status'] == "Draft") $request['status'] = ['Draft', 'Rejected'];
      if($request['status'] == "Reviewed") $request['status'] = ['Rejected', 'Approved'];
      // Special case for Approved tab - show both Approved and Published content
      if($request['status'] == "Approved") $request['status'] = ['Approved', 'Published'];
      // if($request['media_type'] == "image") $request['media_type'] = ['Photo', 'Illustration'];
      switch ($request['media_type']) {
        case 'image':
          $request['media_type'] = ['Photo', 'Illustration'];
          break;
        case 'video':
          $request['media_type'] = ['Video'];
          break;
        case 'audio':
          $request['media_type'] = ['Audio'];
          break;
        case 'document':
          $request['media_type'] = ['Document'];
          break;
        case 'index':
          $request['media_type'] = ['Photo', 'Illustration', 'Video', 'Audio', 'Document'];
          break;
        default:
          break;
      }

      return $this->contentRepository->getContent($request);
    } catch (\Exception $e) {
      error_log($e);
      Log::error($e);
      throw $e;
    }
  }

  public function countContentByAuthor($request)
  {
    $counts = $this->contentRepository->countContentByAuthor($request);

    // Convert the Eloquent model to a plain array with numeric values
    $result = [];
    foreach ((array)$counts as $key => $value) {
      // Skip Eloquent internal properties
      if (in_array($key, ['incrementing', 'preventsLazyLoading', 'exists', 'wasRecentlyCreated', 'timestamps', 'usesUniqueIds'])) {
        continue;
      }
      $result[$key] = (int)($value ?? 0);
    }

    return $result;
  }


  public function create(array $request)
  {
    DB::beginTransaction();
      try {
        $time = now()->format('dmyHis');
        $data = [];
        $i = 1;
        $userId = auth()->user()->id;

        // Ensure user directories exist
        StorageHelper::ensureUserDirectoriesExist($userId);

        foreach($request['file'] as $file){
          // decide orientation
          list($width, $height) = getimagesize($file);
          $orientation = $width > $height ? 'Landscape' : 'Portrait';

          // check media type
          $mime_type = explode('/', $file->getMimeType());
          $media_type = ucfirst($mime_type[0]);

          // Validate image dimensions (minimum 2000x2000 pixels or 4MP)
          if ($media_type == 'Image') {
            $megapixels = ($width * $height) / 1000000;
            if ($width < 2000 || $height < 2000 || $megapixels < 4) {
              throw new \Exception('Image dimensions must be at least 2000x2000 pixels or 4 megapixels.', 400);
            }
          }

          // Validate video resolution for video files
          if ($media_type == 'Video') {
            // Video validation will be handled by FFMpeg when creating thumbnail
          }

          // store original file
          $name_format = 'GML'.$time.$i;
          $filename = $name_format.'.'.$file->extension();

          // Get user-specific storage path
          $file_path = StorageHelper::getUserStoragePath($userId, $mime_type[0], $filename);

          // Store the file in the user's directory
          Storage::disk('public')->putFileAs(
              dirname($file_path),
              $file,
              basename($file_path)
          );

          // create thumbnail
          if($media_type == 'Image'){
            $extension = strtolower($file->getClientOriginalExtension());

            $media_type = 'Photo';
            $thumbnail_filename = $name_format.'.jpg';
            $thumbnail_path = StorageHelper::getUserThumbnailPath($userId, $thumbnail_filename);

            if (in_array($extension, ['tif', 'tiff'])) {
              // Proses TIFF -> JPG menggunakan Imagick
              $imagick = new \Imagick();
              $imagick->readImage($file->getPathname());
              $imagick->setImageFormat('jpg');

              // Create directory if it doesn't exist
              $thumbnailDir = dirname(storage_path("app/public/{$thumbnail_path}"));
              if (!file_exists($thumbnailDir)) {
                  mkdir($thumbnailDir, 0755, true);
              }

              $tempJpg = storage_path("app/public/{$thumbnail_path}");
              $imagick->writeImage($tempJpg);
              $imagick->clear();
              $imagick->destroy();

            } else {
              // Untuk image biasa (jpg/png/webp)
              $image = Image::read($file);
              // Resize sesuai orientasi
              if($width > $height) $image = Image::read($file)->resize(600, 400)->encode();
              else if($width == $height) $image = Image::read($file)->resize(400, 400)->encode();
              else  $image = Image::read($file)->resize(400, 600)->encode();
              Storage::disk('public')->put($thumbnail_path, (string) $image);
            }
          }else if($media_type == 'Video'){
            $thumbnail_filename = $name_format.'.jpg';
            $thumbnail_path = StorageHelper::getUserThumbnailPath($userId, $thumbnail_filename);

            // Get video dimensions and validate resolution
            $ffprobe = \FFMpeg\FFProbe::create();
            $videoInfo = $ffprobe->format(storage_path('app/public/'.$file_path));
            $videoStream = $ffprobe->streams(storage_path('app/public/'.$file_path))->videos()->first();

            if ($videoStream) {
                $videoWidth = $videoStream->get('width');
                $videoHeight = $videoStream->get('height');

                // Check if video meets resolution requirements (HD, Full HD, 2K, or 4K)
                $isHD = ($videoHeight >= 720);
                $isFullHD = ($videoWidth >= 1920 && $videoHeight >= 1080);
                $is2K = ($videoWidth >= 2048 && $videoHeight >= 1080);
                $is4K = ($videoWidth >= 3840 && $videoHeight >= 2160);

                // Get file size in MB
                $fileSize = $videoInfo->get('size') / 1048576;

                // Validate video meets either resolution or size requirements
                if (!($isHD || $isFullHD || $is2K || $is4K || $fileSize <= 256)) {
                    throw new \Exception('Video must be HD (720p), Full HD (1080p), 2K, 4K resolution or maximum 256 MB in size.', 400);
                }
            }

            FFMpeg::fromDisk('public')
              ->open($file_path)
              ->getFrameFromSeconds(2)
              ->export()
              ->toDisk('public')
              ->save($thumbnail_path);

          }else if($media_type == 'Audio'){
            // Use SVG thumbnail for audio
            $thumbnail_path = 'thumbnail/thumbnail_audio.svg';

          }else if(in_array($mime_type[1], ['vnd.openxmlformats-officedocument.spreadsheetml.sheet', 'vnd.ms-excel', 'vnd.ms-excel.sheet.macroEnabled.12'])){
            // Handle Excel files
            $media_type = 'Document';
            $thumbnail_path = 'thumbnail/thumbnail_excel.png';
            
          }else{
            $media_type = 'Document';
            // Use static thumbnails for documents
            if($mime_type[1] == 'pdf') $thumbnail_path = 'thumbnail/thumbnail_pdf.png';
            else if(in_array($mime_type[1], ['msword', 'vnd.openxmlformats-officedocument.wordprocessingml.document'])){
              $thumbnail_path = 'thumbnail/thumbnail_doc.png';
            } else {
              $thumbnail_path = 'thumbnail/thumbnail_file.png';
            }
          }

          $data[] = [
            'id' => Uuid::uuid4(),
            'author_id' => auth()->user()->id,
            'filename' => $filename,
            'original_filename' => $file->getClientOriginalName(),
            'file_path' => $file_path,
            'thumbnail_path' => $thumbnail_path,
            'media_type' => $media_type,
            'orientation' => $orientation,
            'status' => 'Draft',
            'upload_date' => now(),
            'created_at' => now(),
            'updated_at' => now(),
          ];

        $i++;
        }

        $this->contentRepository->create($data);

        foreach($data as $item){
          $this->logRepository->log(
            auth()->user()->id,
            $item['id']->toString(),
            'telah melakukan upload file.',
            'content',
            'file-up'
          );
        }

        DB::commit();

        return $data;
      } catch (\Exception $e) {
        DB::rollback();
        error_log($e);
        Log::error($e);
        throw $e;
      }
  }

  public function update(array $request)
  {
    DB::beginTransaction();
    try {
      $keywords = explode(',', $request['keyword']);
      $keywordIds = [];

      if(count($keywords) < 5 && $request['update_type'] == 'submit'){
        throw new \Exception('Kata kunci tidak boleh kurang dari 5 kata.', 400);
      }

      if($request['keyword'] != null){
        foreach($keywords as $key){
          $keywordIds[] = $this->keywordRepository->create(['name' => $key]);
        }
      }

      if($request['description'] && $request['update_type'] == 'submit'){
        $desCount = preg_match_all('/\b[\p{L}\p{N}]+\b/u', $request['description']);
        if($desCount < 2){
          throw new \Exception('Deskripsi tidak boleh kurang dari 2 kata.', 400);
        }
      }

      $release_doc_path = null;
      if(isset($request['release_document']) && $request['release_document']->isValid()){
        $userId = auth()->user()->id;
        $filename = 'release_' . now()->format('dmyHis') . '.' . $request['release_document']->extension();
        $release_doc_path = StorageHelper::getUserReleaseDocumentPath($userId, $filename);

        // Ensure user directories exist
        StorageHelper::ensureUserDirectoriesExist($userId);

        // Store the file in the user's directory
        Storage::disk('public')->putFileAs(
            dirname($release_doc_path),
            $request['release_document'],
            basename($release_doc_path)
        );
      }

      $status = $request['update_type'] == 'submit' ? 'Pending':'Draft';
      $data = [
        'description' => $request['description'],
        'category_id' => $request['category_id'],
        'category_2_id' => $request['category_2_id'],
        'media_type' => $request['media_type'],
        'creator_name' => $request['creator_name'],
        'date_taken' => Carbon::create($request['date_taken']),
        'release_document' => $release_doc_path,
        'status' => $status,
      ];

      foreach($request['content_id'] as $item){
        $content = $this->contentRepository->findOne($item);

        if($content->release_document) $data['release_document'] = $content->release_document;

        $content_updated = $this->contentRepository->update($item, $data);

        if($keywordIds && $content){
          $content->keywords()->sync($keywordIds);
        }

        $this->logRepository->log(
          auth()->user()->id,
          $item,
          'telah melakukan '.$request['update_type'].' file.',
          'content',
          'update-file'
        );
      }

      if($request['update_type'] == "submit"){
        $supervisor = User::whereHas('role', function($qry) {
          $qry->where('id', 3);
        })->pluck('id');

        foreach($supervisor as $spv){
          $this->notificationRepository->create(
            $spv,
            auth()->user()->id,
            "Submit",
            "telah melakukan submit konten"
          );
        }
      }

      DB::commit();

      return $content_updated;
    } catch (\Exception $th) {
      DB::rollback();
      error_log($th);
      Log::error($th);
      throw $th;
    }
  }

  public function delete(string $id)
  {
    DB::beginTransaction();
    try {
      $content = $this->contentRepository->findOne($id);
      $content_deleted = $this->contentRepository->delete($id);

      if($content_deleted){
        $this->logRepository->log(
          auth()->user()->id,
          null,
          'telah menghapus konten dengan nama file: '.$content->filename.'.',
          'content',
          'file-deleted'
        );
      }

      DB::commit();

      return $content_deleted;

    } catch (\Exception $e) {
      DB::rollback();
      error_log($e);
      Log::error($e);
      throw $e;
    }
  }

  public function bulkDelete(array $request)
  {
    DB::beginTransaction();
    try {
      $bulkDelete = $this->contentRepository->bulkDelete($request);

      if($bulkDelete){
        $this->logRepository->log(
          auth()->user()->id,
          null,
          'telah melakukan hapus konten masal.',
          'content',
          'file-deleted'
        );
      }

      DB::commit();

      return $bulkDelete;
    } catch (\Exception $e) {
      DB::rollback();
      error_log($e);
      Log::error($e);
      throw $e;
    }
  }

  public function getCategory(string $request, string $categoryType = 'all')
  {
    return $this->categoryRepository->getCategory($request, $categoryType);
  }

  public function getAllKeywords($request)
  {
    try {
      return $this->keywordRepository->getKeyword($request);
    } catch (\Exception $e) {
      error_log($e);
      Log::error($e);
      throw $e;
    }
  }

  public function getKeywords(string $id)
  {
    $content = $this->contentRepository->findOne($id);

    return $content->keywords()->pluck('name');
  }

  public function searchKeywords(string $request)
  {
    return $this->keywordRepository->search($request);
  }

  public function getReviews($request)
  {
    try {
      $data = $this->reviewRepository->getReviews($request);
      foreach($data as $item){
        $item['review_date'] = Carbon::parse($item['review_date'])->timezone('Asia/Jakarta')->format('d-m-Y H:i');
      }
      return $data;
    } catch (\Exception $e) {
      error_log($e);
      Log::error($e);
      throw $e;
    }
  }
}