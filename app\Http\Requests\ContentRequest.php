<?php

namespace App\Http\Requests;

use App\Repositories\Contracts\ContentRepositoryInterface;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\Validator;

class ContentRequest extends FormRequest
{
    protected $contentRepository;

    public function __construct(ContentRepositoryInterface $contentRepository)
    {
        $this->contentRepository = $contentRepository;
    }
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            // 'file.*' => [
            //     'required',
            //     'file',
            //     'mimes:jpg,jpeg,png,gif,webp,tif,tiff,mp4,avi,mov,wmv,mp3,wav,ogg,pdf,doc,docx,xls,xlsx,xlsm',
            //     'max:51200'
            // ],
            'description' => 'required_if:update_type,submit',
            'keyword' => 'required_if:update_type,submit',
            'category_id' => 'required_if:update_type,submit',
            'category_2_id' => 'nullable',
            'media_type' => 'required_if:update_type,submit',
            'creator_name' => 'required_if:update_type,submit',
            'date_taken' => 'required_if:update_type,submit',
            'release_document' => 'nullable|file|mimes:pdf,doc,docx|max:10240',
            'content_id' => '',
            'update_type' => '',
        ];
    }

    private function isReleaseDocExist(): bool
    {
        $countContent = count($this->content_id);

        if($countContent > 1){
            return false;
        }

        $id = $this->content_id[0];
        $content = $this->contentRepository->findOne($id);
        
        return $content && $content->release_document;
    }

    protected function failedValidation(Validator $validator)
    {
        return redirect()->back()->with([
            'type' => 'error',
            'message' => config('messages.error.update'),
            'errors' => $validator->errors(),
        ]);
    }
}
