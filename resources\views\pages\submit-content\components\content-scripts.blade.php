<script>
  // This script uses global variables defined in main-scripts.blade.php

  function contentData(data) {
      let container = $("#content-loader-"+status.toLowerCase());
      container.empty();
      selectionBanner.hide();

      data.data.forEach(item => {
          itemData[item.id] = item;

          let mediaElement = item.media_type == "Document" || item.media_type == "Audio" ?
            "{{ asset('assets/') }}" + item.thumbnail_path :
            "{{ Storage::url('') }}" + item.thumbnail_path;
          let checkboxElement = "";
          let deleteElement = "";
          let badge = '';
          let reviewDate = moment(item.review[0]?.review_date).format('DD-MM-YYYY HH:mm');
          let reviewIcon = item.status == "Rejected" ? 'ki-cross-circle text-danger':'ki-check-circle text-primary';

          if(item.status == "Draft"){
            checkboxElement = `
              <div class="multiple-select bg-neutral-600 hover:bg-neutral-800 flex absolute top-1 left-1 justify-center items-center rounded-full w-7 h-7 cursor-pointer z-10 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                <div class="w-4 h-4 rounded-sm bg-transparent border-white border-2 unchecked"></div>
                <i class="hidden ki-filled ki-check text-lg text-white checked"></i>
              </div>
            `;

            // Check if user has delete permission (set by Blade in a global variable)
            @canAccess('delete.uploads')
            deleteElement = `
              <div class="menu-item" data-dropdown-dismiss="true">
                <button class="menu-link btn-delete" data-id="${item.id}">
                  <span class="menu-icon">
                      <i class="ki-filled ki-trash"></i>
                  </span>
                  <span class="menu-title">Delete</span>
                </button>
              </div>
            `;
            @endcanAccess
          }

          switch(item.status){
            case "Draft": badge = 'light'; break;
            case "Pending": badge = 'warning'; break;
            case "Rejected": badge = 'danger'; break;
            case "Approved": badge = 'primary'; break;
            case "Published": badge = 'success'; break;
          }

          // Override display status for the Approved tab
          let displayStatus = item.status;
          if (status === "Approved" && item.status === "Published") {
            displayStatus = "Approved";
            badge = 'primary';
          }

          let list = `
          <div class="relative">
            <div class="card flex px-3 flex-row justify-between border border-gray-300 cursor-pointer select-card" data-id="${item.id}">
              <div class="group bg-[--tw-navbar-bg] w-[25%]">
                ${checkboxElement}
                <div class="group/content relative w-full aspect-square p-0 flex justify-center items-center overflow-hidden">
                  <img alt="" class="w-full h-full object-cover group-hover/content:scale-90 transition-transform duration-300 ease-out" src="${mediaElement}"/>
                </div>
                <div class="bg-white border-t-2 card-rounded-b flex flex-col pt-1.5 pb-2">
                  <span class="text-xs font-medium text-gray-900 truncate">${item.original_filename}</span>
                  <div class="flex items-center justify-between grow">
                      <div class="flex items-center grow">
                          <span class="my-0.5 badge badge-sm badge-outline badge-${badge}">${displayStatus}</span>
                      </div>
                  </div>
                </div>
              </div>
              <div class="p-3 w-[75%]">
                <h3 class="text-sm flex items-center">
                  <i class="ki-filled ${reviewIcon} text-xl"></i>
                  <span class="mx-1.5 font-semibold">Hasil Review</span>
                  </h3>
                <p class="mt-1.5 text-xs grid grid-cols-8">
                  <span class="col-span-1">Oleh</span>
                  <span class="col-span-7">: ${item.review[0]?.reviewer?.name}</span>
                </p>
                <p class="mt-1 text-xs grid grid-cols-8">
                  <span class="col-span-1">Tanggal</span>
                  <span class="col-span-7">: ${reviewDate} WIB</span>
                </p>
                <p class="mt-1 text-xs grid grid-cols-8">
                  <span class="col-span-1">Catatan</span>
                  <span class="col-span-7">: ${item.review[0]?.comments ?? '-'}</span>
                </p>
              </div>
            </div>
            <div class="dropdown absolute bottom-1 right-1" data-dropdown="true" data-dropdown-placement="bottom-end">
                <button class="dropdown-toggle btn btn-xs btn-icon btn-light hover:bg-neutral-100 rounded-full border-0">
                    <i class="ki-filled ki-dots-vertical"></i>
                </button>
                <div class="dropdown-content menu-default w-full max-w-[220px]">
                    <div class="menu-item" data-dropdown-dismiss="true">
                        <a href="${'{{ Storage::url("") }}' + item.file_path}" target="_blank" class="menu-link">
                            <span class="menu-icon">
                                <i class="ki-filled ki-exit-right-corner"></i>
                            </span>
                            <span class="menu-title">See large preview</span>
                        </a>
                    </div>
                    ${deleteElement}
                </div>
            </div>
          </div>`;

          let card = `
          <div class="relative">
            <div class="card group bg-[--tw-navbar-bg] border border-gray-300 cursor-pointer select-card" data-id="${item.id}">
              ${checkboxElement}
              <div class="group/content relative w-full aspect-square p-0 flex justify-center items-center overflow-hidden">
                <img alt="" class="w-full h-full object-cover group-hover/content:scale-90 transition-transform duration-300 ease-out" src="${mediaElement}"/>
              </div>
              <div class="bg-white border-t-2 card-rounded-b flex flex-col px-3 py-1.5">
                <span class="text-xs font-medium text-gray-900 truncate">${item.original_filename}</span>
                <div class="flex items-center justify-between grow">
                    <div class="flex items-center grow">
                        <span class="my-0.5 badge badge-sm badge-outline badge-${badge}">${displayStatus}</span>
                    </div>
                </div>
              </div>
            </div>
            <div class="dropdown absolute bottom-1 right-1" data-dropdown="true" data-dropdown-placement="bottom-end">
                <button class="dropdown-toggle btn btn-xs btn-icon btn-light hover:bg-neutral-100 rounded-full border-0">
                    <i class="ki-filled ki-dots-vertical"></i>
                </button>
                <div class="dropdown-content menu-default w-full max-w-[220px]">
                    <div class="menu-item" data-dropdown-dismiss="true">
                        <a href="${'{{ Storage::url("") }}' + item.file_path}" target="_blank" class="menu-link">
                            <span class="menu-icon">
                                <i class="ki-filled ki-exit-right-corner"></i>
                            </span>
                            <span class="menu-title">See large preview</span>
                        </a>
                    </div>
                    ${deleteElement}
                </div>
            </div>
          </div>`;

          container.append(item.status == "Rejected" ? list:card);
      });

      let $paginationContainer = $("#pagination-container");
      $paginationContainer.empty();

      if (data.links) {
          let active = `class="z-10 page-link flex items-center justify-center px-3 h-8 leading-tight text-blue-600 border border-blue-300 bg-blue-50 hover:bg-blue-100 hover:text-blue-700 dark:border-gray-700 dark:bg-gray-700 dark:text-white"`;
          let inactive = `class="page-link flex items-center justify-center px-3 h-8 leading-tight text-gray-500 bg-white border border-gray-300 hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white"`;

          data.links.forEach(link => {
            let navigation = link.label;
            let pageLink;

            if(navigation.toLowerCase().includes("previous")){
              pageLink = `
                <li>
                  <a href="#" data-url="${link.url}" class="page-link flex items-center justify-center px-3 h-8 ms-0 leading-tight text-gray-500 bg-white border border-e-0 border-gray-300 rounded-s-lg hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
                    <span class="sr-only">${link.label}</span>
                    <svg class="w-2.5 h-2.5 rtl:rotate-180" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                      <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 1 1 5l4 4"/>
                    </svg>
                  </a>
                </li>
              `;
            }else if(navigation.toLowerCase().includes("next")){
              pageLink = `
                <li>
                  <a href="#" data-url="${link.url}" class="page-link flex items-center justify-center px-3 h-8 leading-tight text-gray-500 bg-white border border-gray-300 rounded-e-lg hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
                    <span class="sr-only">${link.label}</span>
                    <svg class="w-2.5 h-2.5 rtl:rotate-180" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                      <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4"/>
                    </svg>
                  </a>
                </li>
              `;
            }else{
              pageLink = `
                <li>
                  <a href="#" data-url="${link.url}" ${link.active ? active+' aria-current="page"' : inactive}>${link.label}</a>
                </li>
              `;
            }

            $paginationContainer.append(pageLink);
          });

          $(".page-link").on("click", function (e) {
              e.preventDefault();
              let url = $(this).data("url");
              detailContent.html(emptyContent);
              getContent(status, filter, authorId, url);
          });
      }

      // Event handlers for card selection and other interactions
      setupCardInteractions();
    }

  function setupCardInteractions() {
      // We're not setting up event handlers here anymore
      // All event handlers are now in main-scripts.blade.php using event delegation
      // This function is kept for backward compatibility
    }

  // updateSelection function moved to main-scripts.blade.php
</script>
